.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
}

.modal-dialog {
    width: 100%;
    max-width: 900px;
    margin: 1.75rem auto;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.modal-header-background {
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
    max-height: calc(100vh - 210px);
    overflow-y: auto;
}

.form-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-sections-service-areas {
    padding-top: 1.25rem;
}

.form-section {
    background-color: #f8f9fa;
    padding: 1.25rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.form-section h4 {
    margin-bottom: 1.25rem;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.certification-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    margin-top: 0;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
}

.success-message {
    color: #198754;
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease-in-out;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.success-message.show {
    opacity: 1;
    transform: translateX(0);
}

.success-message i {
    font-size: 1.2rem;
}

.loading-container, .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.error-container {
    color: #dc3545;
}

.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.modal-header-background {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-header .btn-close {
    padding: 1rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
}

.alert {
    margin-bottom: 1rem;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 1.25rem 1rem;
}

.input-group .country-code {
    border-right: 0;
    border-radius: 0.25rem 0 0 0.25rem;
}

.input-group .form-control:not(.country-code) {
    border-left: 1px solid #dee2e6;
    border-radius: 0 0.25rem 0.25rem 0;
}

.input-group-text {
    background-color: #f8f9fa;
}

.service-areas-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.service-area-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.service-area-input-group .form-control {
    flex: 1;
}

.service-area-input-group .btn {
    padding: 0.375rem 0.75rem;
}

.service-areas-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.service-area-tag {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px 12px;
    margin-bottom: 5px;
}

.service-area-tag span {
    margin-right: 10px;
}

.service-area-tag.service-area-tag-legacy {
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.remove-button {
    background: #dc3545;
    border: none;
    color: white;
    font-size: 1.2rem;
    line-height: 2;
    width: 20px;
    height: 20px;
    margin-left: 6px;
    cursor: pointer;
    border-radius: 4px;
    padding-bottom:3px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-button:hover {
    background: #c82333;
}

.service-area-tag .legacy-indicator {
    color: #856404;
    font-weight: bold;
    margin-left: 4px;
}

/* Schedule Display Styles */
.schedule-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
}

.schedule-day-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.schedule-day-name {
    font-weight: 500;
    color: #495057;
    min-width: 80px;
}

.schedule-time-range {
    color: #6c757d;
    font-size: 14px;
}

/* Interactive Schedule Management Styles */
.schedule-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

.schedule-day-row {
    display: flex;
    flex-direction: column;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.2s ease;
}

.schedule-day-row:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Day Header */
.day-header {
    margin-bottom: 10px;
}

.day-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
}

.day-checkbox input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.day-name {
    color: #495057;
}

/* Time Controls */
.time-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    transition: opacity 0.2s ease;
}

.time-controls.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.time-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.time-input-group label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
}

.time-input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    width: 120px;
    transition: border-color 0.2s ease;
}

.time-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.time-input:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.time-separator {
    font-weight: 500;
    color: #6c757d;
    margin-top: 20px;
    font-size: 14px;
}

/* Validation */
.validation-error {
    margin-top: 8px;
}

.validation-error small {
    font-size: 12px;
}

/* Responsive Design for Schedule */
@media (max-width: 768px) {
    .time-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .time-separator {
        margin-top: 0;
        align-self: center;
    }

    .time-input {
        width: 100%;
        max-width: 200px;
    }
}
