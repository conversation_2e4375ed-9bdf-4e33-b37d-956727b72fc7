﻿// Application/Scheduling/Specifications/BookingByDateSpec.cs
public class BookingByDateSpec : Specification<Booking>
{
  public BookingByDateSpec(Guid walkerId, DateTime date)
  {
    var startOfDay = date.Date;
    var endOfDay = startOfDay.AddDays(1);

    Query.Where(b => b.<PERSON>alkerId == walkerId &&
                     b.Start >= startOfDay &&
                     b.Start < endOfDay);
  }
}
