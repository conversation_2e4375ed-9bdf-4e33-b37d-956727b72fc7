﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.SharedKernel" />
    <PackageReference Include="Ardalis.SmartEnum" />
    <PackageReference Include="Ardalis.Specification" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="BookingAggegate\Validation\" />
    <Folder Include="ClientAggregate\Docs\" />
    <Folder Include="LocationAggregate\Specifications\" />
    <Folder Include="PetWalkerAggregate\Common\" />
    <Folder Include="Services\" />
  </ItemGroup>
</Project>
