﻿namespace FurryFriends.Infrastructure.Data.Config;

public class BookingConfiguration : IEntityTypeConfiguration<Booking>
{
  public void Configure(EntityTypeBuilder<Booking> builder)
  {
    builder.ToTable("Bookings");

    builder.<PERSON>Key(b => b.Id);

    builder.Property(b => b.Id)
        .ValueGeneratedOnAdd()
        .IsRequired();

    builder.Property(b => b.<PERSON>d)
        .IsRequired();

    builder.Property(b => b.Pet<PERSON>wner<PERSON>d)
        .IsRequired();

    builder.Property(b => b.Start)
        .IsRequired();

    builder.Property(b => b.End)
        .IsRequired();

    builder.HasOne(b => b.<PERSON>Walker)
        .WithMany()
        .HasForeignKey(b => b.<PERSON>WalkerId)
        .OnDelete(DeleteBehavior.Restrict);

    builder.HasOne(b => b.<PERSON>Owner)
        .WithMany()
        .HasForeign<PERSON>ey(b => b.<PERSON>d)
        .OnDelete(DeleteBehavior.Restrict);
  }
}
